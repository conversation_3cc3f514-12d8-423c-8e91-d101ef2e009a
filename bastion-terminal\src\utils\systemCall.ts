// 本地服务器地址
const LOCAL_SERVER_URL = 'http://localhost:8899'

// 系统调用工具类
export class SystemCallUtil {
  /**
   * 调用本地终端工具
   * 首先尝试本地HTTP服务，如果失败则使用模拟模式
   */
  static async executeCommand(command: string, args: string): Promise<{
    success: boolean
    message: string
    pid?: number
  }> {
    try {
      console.log(`执行命令: ${command}`)
      console.log(`参数: ${args}`)

      // 尝试调用本地服务
      const response = await fetch(`${LOCAL_SERVER_URL}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ command, args })
      })

      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status}`)
      }

      const result = await response.json()
      return result
    } catch (error) {
      console.error('调用本地服务失败，使用模拟模式:', error)

      // 模拟执行命令
      return this.simulateExecuteCommand(command, args)
    }
  }

  /**
   * 模拟执行命令（当本地服务不可用时）
   */
  private static async simulateExecuteCommand(command: string, args: string): Promise<{
    success: boolean
    message: string
    pid?: number
  }> {
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 检查常见工具路径
    const commonTools = [
      'C:\\Windows\\System32\\notepad.exe',
      'C:\\Program Files\\NetSarang\\Xshell 7\\Xshell.exe',
      'C:\\Program Files (x86)\\Mobatek\\MobaXterm\\MobaXterm.exe',
      'C:\\Program Files\\PuTTY\\putty.exe',
      'C:\\Program Files (x86)\\WinSCP\\WinSCP.exe'
    ]

    if (!commonTools.includes(command)) {
      return {
        success: false,
        message: `工具程序不存在: ${command}`
      }
    }

    // 模拟成功启动
    const mockPid = Math.floor(Math.random() * 10000) + 1000

    // 尝试使用浏览器API启动程序（仅在某些环境下可用）
    try {
      if ('shell' in window && 'openExternal' in (window as any).shell) {
        // Electron环境
        (window as any).shell.openExternal(command)
      } else {
        // 浏览器环境，显示提示信息
        const fullCommand = `${command} ${args}`
        console.log('模拟执行命令:', fullCommand)

        // 创建一个临时的提示框
        const notification = document.createElement('div')
        notification.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: #4CAF50;
          color: white;
          padding: 15px 20px;
          border-radius: 5px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.2);
          z-index: 10000;
          font-family: Arial, sans-serif;
          max-width: 400px;
        `
        notification.innerHTML = `
          <strong>模拟执行命令:</strong><br>
          ${command}<br>
          <small>参数: ${args}</small><br>
          <small style="opacity: 0.8;">在实际环境中，这将启动 ${command.split('\\').pop()}</small>
        `

        document.body.appendChild(notification)

        // 3秒后自动移除
        setTimeout(() => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification)
          }
        }, 3000)
      }
    } catch (e) {
      console.log('无法启动外部程序:', e)
    }

    return {
      success: true,
      message: `模拟启动程序成功，进程ID: ${mockPid}`,
      pid: mockPid
    }
  }

  /**
   * 检查工具是否存在
   */
  static async checkToolExists(toolPath: string): Promise<boolean> {
    try {
      const response = await fetch(`${LOCAL_SERVER_URL}/check-tool`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ toolPath })
      })

      if (!response.ok) {
        throw new Error('服务不可用')
      }

      const result = await response.json()
      return result.exists
    } catch (error) {
      console.error('检查工具失败，使用模拟模式:', error)

      // 模拟检查常见工具
      const commonTools = [
        'C:\\Windows\\System32\\notepad.exe',
        'C:\\Program Files\\NetSarang\\Xshell 7\\Xshell.exe',
        'C:\\Program Files (x86)\\Mobatek\\MobaXterm\\MobaXterm.exe',
        'C:\\Program Files\\PuTTY\\putty.exe',
        'C:\\Program Files (x86)\\WinSCP\\WinSCP.exe'
      ]

      return commonTools.includes(toolPath)
    }
  }

  /**
   * 获取系统信息
   */
  static getSystemInfo(): {
    platform: string
    arch: string
    version: string
  } {
    // 在实际环境中，这里应该获取真实的系统信息
    return {
      platform: 'win32',
      arch: 'x64',
      version: '10.0.19042'
    }
  }

  /**
   * 打开文件选择对话框
   */
  static async selectFile(filters?: {
    name: string
    extensions: string[]
  }[]): Promise<string | null> {
    // 在实际环境中，这里应该打开文件选择对话框
    // 例如：使用 Electron 的 dialog.showOpenDialog()
    
    // 模拟文件选择
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const mockPaths = [
      'C:\\Program Files\\NetSarang\\Xshell 7\\Xshell.exe',
      'C:\\Program Files (x86)\\Mobatek\\MobaXterm\\MobaXterm.exe',
      'C:\\Program Files\\PuTTY\\putty.exe'
    ]
    
    return mockPaths[Math.floor(Math.random() * mockPaths.length)]
  }

  /**
   * 复制文本到剪贴板
   */
  static async copyToClipboard(text: string): Promise<boolean> {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text)
        return true
      } else {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = text
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()
        
        const success = document.execCommand('copy')
        document.body.removeChild(textArea)
        return success
      }
    } catch (error) {
      console.error('复制到剪贴板失败:', error)
      return false
    }
  }

  /**
   * 验证网络连接
   */
  static async testConnection(host: string, port: number): Promise<{
    success: boolean
    latency?: number
    error?: string
  }> {
    try {
      const response = await fetch(`${LOCAL_SERVER_URL}/test-connection`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ host, port })
      })

      if (!response.ok) {
        throw new Error('服务不可用')
      }

      const result = await response.json()
      return result
    } catch (error) {
      console.error('网络连接测试失败，使用模拟模式:', error)

      // 模拟网络测试
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 模拟80%的成功率
      const success = Math.random() > 0.2

      if (success) {
        return {
          success: true,
          latency: Math.floor(Math.random() * 100) + 10 // 10-110ms
        }
      } else {
        return {
          success: false,
          error: '连接超时或主机不可达'
        }
      }
    }
  }

  /**
   * 获取进程列表
   */
  static async getProcessList(): Promise<Array<{
    pid: number
    name: string
    command: string
    startTime?: Date
  }>> {
    try {
      const response = await fetch(`${LOCAL_SERVER_URL}/get-processes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (!response.ok) {
        throw new Error('服务不可用')
      }

      const result = await response.json()
      return result.map((process: any) => ({
        ...process,
        startTime: process.startTime ? new Date(process.startTime) : new Date()
      }))
    } catch (error) {
      console.error('获取进程列表失败，使用模拟模式:', error)

      // 模拟进程列表
      const mockProcesses = [
        {
          pid: 1234,
          name: 'notepad.exe',
          command: 'C:\\Windows\\System32\\notepad.exe',
          startTime: new Date(Date.now() - Math.random() * 3600000)
        },
        {
          pid: 5678,
          name: 'MobaXterm.exe',
          command: 'C:\\Program Files (x86)\\Mobatek\\MobaXterm\\MobaXterm.exe -newtab "ssh root@192.168.119.80 -p 22"',
          startTime: new Date(Date.now() - Math.random() * 1800000)
        }
      ]

      // 随机返回0-2个进程
      const count = Math.floor(Math.random() * 3)
      return mockProcesses.slice(0, count)
    }
  }

  /**
   * 终止进程
   */
  static async killProcess(pid: number): Promise<boolean> {
    try {
      const response = await fetch(`${LOCAL_SERVER_URL}/kill-process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ pid })
      })

      if (!response.ok) {
        throw new Error('服务不可用')
      }

      const result = await response.json()
      return result.success
    } catch (error) {
      console.error('终止进程失败，使用模拟模式:', error)

      // 模拟终止进程
      await new Promise(resolve => setTimeout(resolve, 500))

      console.log(`模拟终止进程: ${pid}`)

      // 90%的成功率
      return Math.random() > 0.1
    }
  }
}

// 导出单例
export const systemCall = new SystemCallUtil()
