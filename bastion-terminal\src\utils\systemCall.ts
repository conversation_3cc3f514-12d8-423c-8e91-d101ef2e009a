// 本地服务器地址
const LOCAL_SERVER_URL = 'http://localhost:8899'

// 系统调用工具类
export class SystemCallUtil {
  /**
   * 调用本地终端工具
   * 通过本地HTTP服务来执行系统命令
   */
  static async executeCommand(command: string, args: string): Promise<{
    success: boolean
    message: string
    pid?: number
  }> {
    try {
      console.log(`执行命令: ${command}`)
      console.log(`参数: ${args}`)

      const response = await fetch(`${LOCAL_SERVER_URL}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ command, args })
      })

      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status}`)
      }

      const result = await response.json()
      return result
    } catch (error) {
      console.error('调用本地服务失败:', error)

      // 如果本地服务不可用，提供友好的错误信息
      if (error instanceof TypeError && error.message.includes('fetch')) {
        return {
          success: false,
          message: '本地服务未启动，请先运行: node local-server.js'
        }
      }

      return {
        success: false,
        message: `执行失败: ${(error as Error).message}`
      }
    }
  }

  /**
   * 检查工具是否存在
   */
  static async checkToolExists(toolPath: string): Promise<boolean> {
    try {
      const response = await fetch(`${LOCAL_SERVER_URL}/check-tool`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ toolPath })
      })

      if (!response.ok) {
        return false
      }

      const result = await response.json()
      return result.exists
    } catch (error) {
      console.error('检查工具失败:', error)
      return false
    }
  }

  /**
   * 获取系统信息
   */
  static getSystemInfo(): {
    platform: string
    arch: string
    version: string
  } {
    // 在实际环境中，这里应该获取真实的系统信息
    return {
      platform: 'win32',
      arch: 'x64',
      version: '10.0.19042'
    }
  }

  /**
   * 打开文件选择对话框
   */
  static async selectFile(filters?: {
    name: string
    extensions: string[]
  }[]): Promise<string | null> {
    // 在实际环境中，这里应该打开文件选择对话框
    // 例如：使用 Electron 的 dialog.showOpenDialog()
    
    // 模拟文件选择
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const mockPaths = [
      'C:\\Program Files\\NetSarang\\Xshell 7\\Xshell.exe',
      'C:\\Program Files (x86)\\Mobatek\\MobaXterm\\MobaXterm.exe',
      'C:\\Program Files\\PuTTY\\putty.exe'
    ]
    
    return mockPaths[Math.floor(Math.random() * mockPaths.length)]
  }

  /**
   * 复制文本到剪贴板
   */
  static async copyToClipboard(text: string): Promise<boolean> {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text)
        return true
      } else {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = text
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()
        
        const success = document.execCommand('copy')
        document.body.removeChild(textArea)
        return success
      }
    } catch (error) {
      console.error('复制到剪贴板失败:', error)
      return false
    }
  }

  /**
   * 验证网络连接
   */
  static async testConnection(host: string, port: number): Promise<{
    success: boolean
    latency?: number
    error?: string
  }> {
    try {
      const response = await fetch(`${LOCAL_SERVER_URL}/test-connection`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ host, port })
      })

      if (!response.ok) {
        return {
          success: false,
          error: '网络测试服务不可用'
        }
      }

      const result = await response.json()
      return result
    } catch (error) {
      console.error('网络连接测试失败:', error)
      return {
        success: false,
        error: '网络测试失败'
      }
    }
  }

  /**
   * 获取进程列表
   */
  static async getProcessList(): Promise<Array<{
    pid: number
    name: string
    command: string
    startTime?: Date
  }>> {
    try {
      const response = await fetch(`${LOCAL_SERVER_URL}/get-processes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (!response.ok) {
        return []
      }

      const result = await response.json()
      return result.map((process: any) => ({
        ...process,
        startTime: process.startTime ? new Date(process.startTime) : new Date()
      }))
    } catch (error) {
      console.error('获取进程列表失败:', error)
      return []
    }
  }

  /**
   * 终止进程
   */
  static async killProcess(pid: number): Promise<boolean> {
    try {
      const response = await fetch(`${LOCAL_SERVER_URL}/kill-process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ pid })
      })

      if (!response.ok) {
        return false
      }

      const result = await response.json()
      return result.success
    } catch (error) {
      console.error('终止进程失败:', error)
      return false
    }
  }
}

// 导出单例
export const systemCall = new SystemCallUtil()
