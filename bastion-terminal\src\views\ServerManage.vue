<template>
  <div class="server-manage">
    <!-- 导航栏 -->
    <AppNavigation />
    
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1>
            <el-icon><Monitor /></el-icon>
            服务器管理
          </h1>
          <p>管理和配置服务器连接信息</p>
        </div>
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          添加服务器
        </el-button>
      </div>
    </div>

    <div class="page-content">
      <!-- 筛选和搜索 -->
      <el-card shadow="never" class="filter-card">
        <div class="filter-section">
          <div class="filter-left">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索服务器..."
              clearable
              style="width: 300px"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select
              v-model="selectedGroup"
              placeholder="选择分组"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="group in serverGroups"
                :key="group"
                :label="group"
                :value="group"
              />
            </el-select>
          </div>
          <div class="filter-right">
            <el-button @click="exportServers">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
            <el-button @click="importServers">
              <el-icon><Upload /></el-icon>
              导入
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 服务器表格 -->
      <el-card shadow="never">
        <el-table :data="filteredServers" style="width: 100%">
          <el-table-column prop="name" label="服务器名称" width="200">
            <template #default="{ row }">
              <div class="server-name-cell">
                <el-icon :color="getStatusColor(row)">
                  <SuccessFilled v-if="row.lastConnected" />
                  <Warning v-else />
                </el-icon>
                <span>{{ row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="host" label="主机地址" width="150" />
          <el-table-column prop="port" label="端口" width="80" />
          <el-table-column prop="username" label="用户名" width="120" />
          <el-table-column prop="group" label="分组" width="120">
            <template #default="{ row }">
              <el-tag size="small">{{ row.group }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="tags" label="标签" width="200">
            <template #default="{ row }">
              <div class="tags-cell">
                <el-tag
                  v-for="tag in row.tags"
                  :key="tag"
                  size="small"
                  type="info"
                  style="margin-right: 4px"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" />
          <el-table-column prop="lastConnected" label="最后连接" width="150">
            <template #default="{ row }">
              <span v-if="row.lastConnected">
                {{ formatDate(row.lastConnected) }}
              </span>
              <span v-else class="no-connection">未连接</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click="handleEdit(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button size="small" type="success" @click="handleConnect(row)">
                <el-icon><Connection /></el-icon>
                连接
              </el-button>
              <el-button 
                size="small" 
                type="danger" 
                @click="handleDelete(row.id)"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 添加/编辑服务器对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingServer ? '编辑服务器' : '添加服务器'"
      width="600px"
    >
      <el-form 
        ref="formRef"
        :model="serverForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="服务器名称" prop="name">
              <el-input v-model="serverForm.name" placeholder="请输入服务器名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分组" prop="group">
              <el-input v-model="serverForm.group" placeholder="请输入分组名称" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="16">
            <el-form-item label="主机地址" prop="host">
              <el-input v-model="serverForm.host" placeholder="请输入IP地址或域名" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="端口" prop="port">
              <el-input-number 
                v-model="serverForm.port" 
                :min="1" 
                :max="65535"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="用户名" prop="username">
          <el-input v-model="serverForm.username" placeholder="请输入登录用户名" />
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input 
            v-model="serverForm.description" 
            type="textarea"
            :rows="3"
            placeholder="请输入服务器描述"
          />
        </el-form-item>
        
        <el-form-item label="标签">
          <el-select
            v-model="serverForm.tags"
            multiple
            filterable
            allow-create
            placeholder="请选择或输入标签"
            style="width: 100%"
          >
            <el-option
              v-for="tag in commonTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave">
          {{ editingServer ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useTerminalStore } from '@/stores/terminal'
import AppNavigation from '@/components/AppNavigation.vue'
import type { ServerInfo } from '@/stores/terminal'

const terminalStore = useTerminalStore()

// 响应式数据
const showAddDialog = ref(false)
const editingServer = ref<ServerInfo | null>(null)
const formRef = ref()
const searchKeyword = ref('')
const selectedGroup = ref('')

// 表单数据
const serverForm = reactive({
  name: '',
  host: '',
  port: 22,
  username: '',
  description: '',
  group: '',
  tags: [] as string[]
})

// 常用标签
const commonTags = ['web', 'database', 'cache', 'nginx', 'mysql', 'redis', 'production', 'test', 'development']

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入服务器名称', trigger: 'blur' }
  ],
  host: [
    { required: true, message: '请输入主机地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  group: [
    { required: true, message: '请输入分组', trigger: 'blur' }
  ]
}

// 计算属性
const serverGroups = computed(() => {
  return Array.from(new Set(terminalStore.servers.map(s => s.group)))
})

const filteredServers = computed(() => {
  let servers = terminalStore.servers
  
  if (searchKeyword.value) {
    servers = servers.filter(server => 
      server.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      server.host.includes(searchKeyword.value) ||
      server.description.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }
  
  if (selectedGroup.value) {
    servers = servers.filter(server => server.group === selectedGroup.value)
  }
  
  return servers
})

// 事件处理
const handleEdit = (server: ServerInfo) => {
  editingServer.value = server
  Object.assign(serverForm, server)
  showAddDialog.value = true
}

const handleDelete = async (serverId: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这个服务器吗？', '确认删除', {
      type: 'warning'
    })
    
    terminalStore.deleteServer(serverId)
    ElMessage.success('服务器已删除')
  } catch {
    // 用户取消删除
  }
}

const handleConnect = (server: ServerInfo) => {
  ElMessage.info(`连接到服务器: ${server.name}`)
  // 这里可以跳转到主页面并自动选择该服务器
}

const handleCancel = () => {
  showAddDialog.value = false
  editingServer.value = null
  resetForm()
}

const handleSave = async () => {
  try {
    await formRef.value.validate()
    
    if (editingServer.value) {
      // 更新服务器
      terminalStore.updateServer(editingServer.value.id, serverForm)
      ElMessage.success('服务器已更新')
    } else {
      // 添加服务器
      terminalStore.addServer(serverForm)
      ElMessage.success('服务器已添加')
    }
    
    handleCancel()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const resetForm = () => {
  Object.assign(serverForm, {
    name: '',
    host: '',
    port: 22,
    username: '',
    description: '',
    group: '',
    tags: []
  })
  formRef.value?.clearValidate()
}

const getStatusColor = (server: ServerInfo) => {
  return server.lastConnected ? '#67C23A' : '#E6A23C'
}

const formatDate = (date: Date) => {
  return date.toLocaleString()
}

const exportServers = () => {
  ElMessage.info('导出功能待实现')
}

const importServers = () => {
  ElMessage.info('导入功能待实现')
}
</script>

<style scoped>
.server-manage {
  min-height: 100vh;
  background: #F5F7FA;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px 24px;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section h1 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 600;
}

.title-section p {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

.page-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.filter-card {
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-left {
  display: flex;
  gap: 16px;
  align-items: center;
}

.filter-right {
  display: flex;
  gap: 12px;
}

.server-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tags-cell {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.no-connection {
  color: #909399;
  font-style: italic;
}

:deep(.el-card) {
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table th) {
  background: #F8F9FA;
}
</style>
