import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'

// 终端工具类型
export interface TerminalTool {
  id: string
  name: string
  icon: string
  path: string
  args: string
  description: string
  enabled: boolean
}

// 服务器信息类型
export interface ServerInfo {
  id: string
  name: string
  host: string
  port: number
  username: string
  password?: string
  privateKey?: string
  description: string
  group: string
  tags: string[]
  lastConnected?: Date
}

// 连接历史类型
export interface ConnectionHistory {
  id: string
  serverId: string
  serverName: string
  toolId: string
  toolName: string
  connectedAt: Date
  duration?: number
  status: 'success' | 'failed' | 'disconnected'
}

export const useTerminalStore = defineStore('terminal', () => {
  // 终端工具列表
  const tools = ref<TerminalTool[]>([
    {
      id: 'xshell',
      name: 'XShell',
      icon: 'Monitor',
      path: 'C:\\Program Files\\NetSarang\\Xshell 7\\Xshell.exe',
      args: '-url ssh://{username}@{host}:{port}',
      description: '专业的SSH客户端工具',
      enabled: true
    },
    {
      id: 'mobaxterm',
      name: 'MobaXterm',
      icon: 'Connection',
      path: 'C:\\Program Files (x86)\\Mobatek\\MobaXterm\\MobaXterm.exe',
      args: '-newtab "ssh {username}@{host} -p {port}"',
      description: '增强型终端工具',
      enabled: true
    },
    {
      id: 'putty',
      name: 'PuTTY',
      icon: 'Terminal',
      path: 'C:\\Program Files\\PuTTY\\putty.exe',
      args: '-ssh {host} -P {port} -l {username}',
      description: '轻量级SSH客户端',
      enabled: true
    },
    {
      id: 'winscp',
      name: 'WinSCP',
      icon: 'FolderOpened',
      path: 'C:\\Program Files (x86)\\WinSCP\\WinSCP.exe',
      args: 'sftp://{username}@{host}:{port}',
      description: 'SFTP文件传输工具',
      enabled: true
    }
  ])

  // 服务器列表
  const servers = ref<ServerInfo[]>([
    {
      id: 'server1',
      name: '生产服务器-Web01',
      host: '*************',
      port: 22,
      username: 'root',
      description: '主要Web服务器',
      group: '生产环境',
      tags: ['web', 'nginx', 'production']
    },
    {
      id: 'server2',
      name: '测试服务器-DB01',
      host: '*************',
      port: 22,
      username: 'admin',
      description: '数据库服务器',
      group: '测试环境',
      tags: ['database', 'mysql', 'test']
    }
  ])

  // 连接历史
  const connectionHistory = ref<ConnectionHistory[]>([])

  // 当前选中的服务器
  const selectedServer = ref<ServerInfo | null>(null)

  // 添加终端工具
  const addTool = (tool: Omit<TerminalTool, 'id'>) => {
    const newTool: TerminalTool = {
      ...tool,
      id: Date.now().toString()
    }
    tools.value.push(newTool)
  }

  // 更新终端工具
  const updateTool = (id: string, updates: Partial<TerminalTool>) => {
    const index = tools.value.findIndex(tool => tool.id === id)
    if (index !== -1) {
      tools.value[index] = { ...tools.value[index], ...updates }
    }
  }

  // 删除终端工具
  const deleteTool = (id: string) => {
    const index = tools.value.findIndex(tool => tool.id === id)
    if (index !== -1) {
      tools.value.splice(index, 1)
    }
  }

  // 添加服务器
  const addServer = (server: Omit<ServerInfo, 'id'>) => {
    const newServer: ServerInfo = {
      ...server,
      id: Date.now().toString()
    }
    servers.value.push(newServer)
  }

  // 更新服务器
  const updateServer = (id: string, updates: Partial<ServerInfo>) => {
    const index = servers.value.findIndex(server => server.id === id)
    if (index !== -1) {
      servers.value[index] = { ...servers.value[index], ...updates }
    }
  }

  // 删除服务器
  const deleteServer = (id: string) => {
    const index = servers.value.findIndex(server => server.id === id)
    if (index !== -1) {
      servers.value.splice(index, 1)
    }
  }

  // 连接服务器
  const connectToServer = async (serverId: string, toolId: string) => {
    const server = servers.value.find(s => s.id === serverId)
    const tool = tools.value.find(t => t.id === toolId)
    
    if (!server || !tool) {
      throw new Error('服务器或工具不存在')
    }

    // 构建命令参数
    let args = tool.args
      .replace('{username}', server.username)
      .replace('{host}', server.host)
      .replace('{port}', server.port.toString())

    // 记录连接历史
    const historyRecord: ConnectionHistory = {
      id: Date.now().toString(),
      serverId: server.id,
      serverName: server.name,
      toolId: tool.id,
      toolName: tool.name,
      connectedAt: new Date(),
      status: 'success'
    }

    connectionHistory.value.unshift(historyRecord)

    // 更新服务器最后连接时间
    updateServer(serverId, { lastConnected: new Date() })

    // 这里应该调用系统API启动工具
    // 由于是前端演示，我们模拟启动过程
    console.log(`启动工具: ${tool.path} ${args}`)
    
    return {
      command: `${tool.path} ${args}`,
      server,
      tool
    }
  }

  // 获取服务器分组
  const getServerGroups = () => {
    const groups = new Set(servers.value.map(server => server.group))
    return Array.from(groups)
  }

  // 根据分组获取服务器
  const getServersByGroup = (group: string) => {
    return servers.value.filter(server => server.group === group)
  }

  // 搜索服务器
  const searchServers = (keyword: string) => {
    if (!keyword) return servers.value
    
    return servers.value.filter(server => 
      server.name.toLowerCase().includes(keyword.toLowerCase()) ||
      server.host.includes(keyword) ||
      server.description.toLowerCase().includes(keyword.toLowerCase()) ||
      server.tags.some(tag => tag.toLowerCase().includes(keyword.toLowerCase()))
    )
  }

  return {
    tools,
    servers,
    connectionHistory,
    selectedServer,
    addTool,
    updateTool,
    deleteTool,
    addServer,
    updateServer,
    deleteServer,
    connectToServer,
    getServerGroups,
    getServersByGroup,
    searchServers
  }
})
