<template>
  <el-card shadow="never" class="process-manager">
    <template #header>
      <div class="header">
        <h3>
          <el-icon><Monitor /></el-icon>
          运行中的终端
        </h3>
        <div class="header-actions">
          <el-button size="small" @click="refreshProcesses">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button size="small" type="danger" @click="killAllProcesses">
            <el-icon><Close /></el-icon>
            全部关闭
          </el-button>
        </div>
      </div>
    </template>

    <div class="process-list">
      <el-empty 
        v-if="processes.length === 0" 
        description="暂无运行中的终端"
        :image-size="80"
      />
      
      <div v-else class="process-items">
        <div 
          v-for="process in processes" 
          :key="process.pid"
          class="process-item"
        >
          <div class="process-info">
            <div class="process-header">
              <div class="process-name">
                <el-icon color="#67C23A"><CircleCheckFilled /></el-icon>
                <span>{{ process.name }}</span>
              </div>
              <div class="process-pid">
                PID: {{ process.pid }}
              </div>
            </div>
            <div class="process-command">
              {{ getShortCommand(process.command) }}
            </div>
            <div class="process-meta">
              <el-tag size="small" type="success">运行中</el-tag>
              <span class="process-time">{{ formatRunTime(process.startTime) }}</span>
            </div>
          </div>
          <div class="process-actions">
            <el-button 
              size="small" 
              type="danger" 
              @click="killProcess(process.pid)"
            >
              <el-icon><Close /></el-icon>
              终止
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { SystemCallUtil } from '@/utils/systemCall'

interface Process {
  pid: number
  name: string
  command: string
  startTime: Date
}

const processes = ref<Process[]>([])
let refreshTimer: number | null = null

onMounted(() => {
  refreshProcesses()
  // 每5秒自动刷新
  refreshTimer = window.setInterval(refreshProcesses, 5000)
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})

const refreshProcesses = async () => {
  try {
    const processList = await SystemCallUtil.getProcessList()
    processes.value = processList.map(p => ({
      ...p,
      startTime: new Date(Date.now() - Math.random() * 3600000) // 模拟启动时间
    }))
  } catch (error) {
    console.error('获取进程列表失败:', error)
  }
}

const killProcess = async (pid: number) => {
  try {
    await ElMessageBox.confirm('确定要终止这个进程吗？', '确认终止', {
      type: 'warning'
    })
    
    const success = await SystemCallUtil.killProcess(pid)
    if (success) {
      ElMessage.success('进程已终止')
      // 从列表中移除
      processes.value = processes.value.filter(p => p.pid !== pid)
    } else {
      ElMessage.error('终止进程失败')
    }
  } catch {
    // 用户取消
  }
}

const killAllProcesses = async () => {
  if (processes.value.length === 0) {
    ElMessage.info('没有运行中的进程')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要终止所有 ${processes.value.length} 个进程吗？`, 
      '确认终止', 
      {
        type: 'warning'
      }
    )
    
    let successCount = 0
    for (const process of processes.value) {
      const success = await SystemCallUtil.killProcess(process.pid)
      if (success) {
        successCount++
      }
    }
    
    ElMessage.success(`成功终止 ${successCount} 个进程`)
    refreshProcesses()
  } catch {
    // 用户取消
  }
}

const getShortCommand = (command: string) => {
  if (command.length > 60) {
    return command.substring(0, 57) + '...'
  }
  return command
}

const formatRunTime = (startTime: Date) => {
  const now = new Date()
  const diff = now.getTime() - startTime.getTime()
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  
  if (hours > 0) {
    return `运行 ${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `运行 ${minutes}分钟`
  } else {
    return '刚启动'
  }
}
</script>

<style scoped>
.process-manager {
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.process-list {
  max-height: 400px;
  overflow-y: auto;
}

.process-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.process-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #F8F9FA;
  border-radius: 8px;
  border-left: 4px solid #67C23A;
}

.process-info {
  flex: 1;
}

.process-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.process-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.process-pid {
  font-size: 12px;
  color: #909399;
  font-family: 'Courier New', monospace;
}

.process-command {
  font-size: 12px;
  color: #606266;
  font-family: 'Courier New', monospace;
  margin-bottom: 8px;
  line-height: 1.4;
}

.process-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.process-time {
  font-size: 12px;
  color: #909399;
}

.process-actions {
  margin-left: 16px;
}

:deep(.el-card__body) {
  padding: 16px;
}
</style>
