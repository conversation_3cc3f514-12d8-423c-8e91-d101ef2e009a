const http = require('http');
const { spawn } = require('child_process');
const fs = require('fs');

const PORT = 8899;

const server = http.createServer((req, res) => {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  res.setHeader('Content-Type', 'application/json');

  // 处理CORS预检请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  if (req.method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    
    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        const url = new URL(req.url, `http://localhost:${PORT}`);
        
        console.log(`收到请求: ${url.pathname}`, data);
        
        if (url.pathname === '/execute') {
          handleExecute(data, res);
        } else if (url.pathname === '/check-tool') {
          handleCheckTool(data, res);
        } else if (url.pathname === '/test-connection') {
          handleTestConnection(data, res);
        } else if (url.pathname === '/get-processes') {
          handleGetProcesses(res);
        } else {
          res.writeHead(404);
          res.end(JSON.stringify({ error: 'Not found' }));
        }
      } catch (error) {
        console.error('解析请求失败:', error);
        res.writeHead(400);
        res.end(JSON.stringify({ error: 'Invalid JSON' }));
      }
    });
  } else {
    res.writeHead(405);
    res.end(JSON.stringify({ error: 'Method not allowed' }));
  }
});

function handleExecute(data, res) {
  const { command, args } = data;
  
  console.log(`执行命令: ${command}`);
  console.log(`参数: ${args}`);
  
  try {
    // 检查工具是否存在
    if (!fs.existsSync(command)) {
      console.log(`工具不存在: ${command}`);
      res.writeHead(200);
      res.end(JSON.stringify({
        success: false,
        message: `工具程序不存在: ${command}`
      }));
      return;
    }
    
    // 简单的参数解析
    const argArray = args ? args.split(' ').filter(arg => arg.trim()) : [];
    console.log(`解析后的参数:`, argArray);
    
    // 启动进程
    const child = spawn(command, argArray, {
      detached: true,
      stdio: 'ignore'
    });
    
    child.unref();
    const pid = child.pid;
    
    console.log(`进程启动成功，PID: ${pid}`);
    
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      message: `成功启动程序，进程ID: ${pid}`,
      pid: pid
    }));
    
  } catch (error) {
    console.error('执行命令失败:', error);
    res.writeHead(200);
    res.end(JSON.stringify({
      success: false,
      message: `执行失败: ${error.message}`
    }));
  }
}

function handleCheckTool(data, res) {
  const { toolPath } = data;
  console.log(`检查工具: ${toolPath}`);
  
  const exists = fs.existsSync(toolPath);
  console.log(`工具存在: ${exists}`);
  
  res.writeHead(200);
  res.end(JSON.stringify({ exists }));
}

function handleTestConnection(data, res) {
  const { host, port } = data;
  console.log(`测试连接: ${host}:${port}`);
  
  // 简单返回成功
  res.writeHead(200);
  res.end(JSON.stringify({
    success: true,
    latency: 50
  }));
}

function handleGetProcesses(res) {
  console.log('获取进程列表');
  
  // 返回空列表
  res.writeHead(200);
  res.end(JSON.stringify([]));
}

server.listen(PORT, 'localhost', () => {
  console.log(`简单本地服务器运行在 http://localhost:${PORT}`);
  console.log('用于处理堡垒机终端管理系统的本地调用');
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});
