<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>堡垒机终端管理系统 - API测试</h1>
    
    <div class="test-section">
        <h3>1. 检查工具是否存在</h3>
        <button onclick="testCheckTool()">测试检查记事本</button>
        <button onclick="testCheckMobaXterm()">测试检查MobaXterm</button>
        <div id="checkResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 网络连接测试</h3>
        <button onclick="testConnection()">测试连接百度</button>
        <button onclick="testConnectionLocal()">测试连接本地</button>
        <div id="connectionResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 执行命令</h3>
        <button onclick="testExecuteNotepad()">启动记事本</button>
        <button onclick="testExecuteMobaXterm()">启动MobaXterm</button>
        <div id="executeResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>4. 进程管理</h3>
        <button onclick="testGetProcesses()">获取进程列表</button>
        <button onclick="testKillProcess()">终止最后一个进程</button>
        <div id="processResult" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8899';
        let lastPid = null;

        async function apiCall(endpoint, data) {
            try {
                const response = await fetch(`${API_BASE}${endpoint}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                throw new Error(`请求失败: ${error.message}`);
            }
        }

        function showResult(elementId, result, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(result, null, 2);
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
        }

        async function testCheckTool() {
            try {
                const result = await apiCall('/check-tool', {
                    toolPath: 'C:\\Windows\\System32\\notepad.exe'
                });
                showResult('checkResult', result);
            } catch (error) {
                showResult('checkResult', { error: error.message }, false);
            }
        }

        async function testCheckMobaXterm() {
            try {
                const result = await apiCall('/check-tool', {
                    toolPath: 'C:\\Program Files (x86)\\Mobatek\\MobaXterm\\MobaXterm.exe'
                });
                showResult('checkResult', result);
            } catch (error) {
                showResult('checkResult', { error: error.message }, false);
            }
        }

        async function testConnection() {
            try {
                const result = await apiCall('/test-connection', {
                    host: 'www.baidu.com',
                    port: 80
                });
                showResult('connectionResult', result);
            } catch (error) {
                showResult('connectionResult', { error: error.message }, false);
            }
        }

        async function testConnectionLocal() {
            try {
                const result = await apiCall('/test-connection', {
                    host: '127.0.0.1',
                    port: 80
                });
                showResult('connectionResult', result);
            } catch (error) {
                showResult('connectionResult', { error: error.message }, false);
            }
        }

        async function testExecuteNotepad() {
            try {
                const result = await apiCall('/execute', {
                    command: 'C:\\Windows\\System32\\notepad.exe',
                    args: ''
                });
                if (result.success && result.pid) {
                    lastPid = result.pid;
                }
                showResult('executeResult', result);
            } catch (error) {
                showResult('executeResult', { error: error.message }, false);
            }
        }

        async function testExecuteMobaXterm() {
            try {
                const result = await apiCall('/execute', {
                    command: 'C:\\Program Files (x86)\\Mobatek\\MobaXterm\\MobaXterm.exe',
                    args: '-newtab "ssh root@192.168.119.80 -p 22"'
                });
                if (result.success && result.pid) {
                    lastPid = result.pid;
                }
                showResult('executeResult', result);
            } catch (error) {
                showResult('executeResult', { error: error.message }, false);
            }
        }

        async function testGetProcesses() {
            try {
                const result = await apiCall('/get-processes', {});
                showResult('processResult', result);
            } catch (error) {
                showResult('processResult', { error: error.message }, false);
            }
        }

        async function testKillProcess() {
            if (!lastPid) {
                showResult('processResult', { error: '没有可终止的进程，请先启动一个程序' }, false);
                return;
            }

            try {
                const result = await apiCall('/kill-process', {
                    pid: lastPid
                });
                showResult('processResult', result);
                if (result.success) {
                    lastPid = null;
                }
            } catch (error) {
                showResult('processResult', { error: error.message }, false);
            }
        }

        // 页面加载时检查服务状态
        window.onload = async function() {
            try {
                await testCheckTool();
                console.log('本地服务连接成功');
            } catch (error) {
                console.error('本地服务连接失败:', error);
                alert('本地服务未启动，请先运行: node local-server.js');
            }
        };
    </script>
</body>
</html>
