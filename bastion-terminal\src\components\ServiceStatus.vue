<template>
  <el-alert
    v-if="!serviceAvailable"
    title="本地服务未启动"
    type="warning"
    :closable="false"
    show-icon
    class="service-alert"
  >
    <template #default>
      <div class="service-message">
        <p>为了能够真正启动本地终端工具，需要启动本地服务。</p>
        <div class="service-instructions">
          <h4>启动方法：</h4>
          <ol>
            <li>打开命令行，进入项目目录</li>
            <li>运行以下命令之一：</li>
            <ul>
              <li><code>python local-server.py</code> (推荐)</li>
              <li><code>node local-server.js</code></li>
            </ul>
            <li>或者双击运行 <code>start-python-server.bat</code></li>
          </ol>
        </div>
        <div class="service-actions">
          <el-button size="small" @click="checkService">
            <el-icon><Refresh /></el-icon>
            重新检查
          </el-button>
          <el-button size="small" type="primary" @click="showInstructions = !showInstructions">
            <el-icon><QuestionFilled /></el-icon>
            {{ showInstructions ? '隐藏' : '显示' }}详细说明
          </el-button>
        </div>
        
        <el-collapse-transition>
          <div v-show="showInstructions" class="detailed-instructions">
            <el-divider />
            <h4>详细说明：</h4>
            <p><strong>当前模式：</strong> 模拟模式 - 不会真正启动程序，仅显示模拟效果</p>
            <p><strong>完整功能：</strong> 需要启动本地服务来真正调用系统程序</p>
            
            <h5>本地服务功能：</h5>
            <ul>
              <li>真正启动本地终端工具（XShell、MobaXterm、PuTTY等）</li>
              <li>检查工具程序是否存在</li>
              <li>网络连接测试</li>
              <li>进程管理和监控</li>
            </ul>
            
            <h5>安全说明：</h5>
            <p>本地服务仅监听 localhost:8899，不会暴露到外网，确保安全性。</p>
          </div>
        </el-collapse-transition>
      </div>
    </template>
  </el-alert>
  
  <el-alert
    v-else
    title="本地服务已连接"
    type="success"
    :closable="false"
    show-icon
    class="service-alert"
  >
    <template #default>
      <p>本地服务运行正常，可以真正启动终端工具。</p>
    </template>
  </el-alert>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const serviceAvailable = ref(false)
const showInstructions = ref(false)
let checkInterval: number | null = null

const checkService = async () => {
  try {
    const response = await fetch('http://localhost:8899/check-tool', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ toolPath: 'test' })
    })
    
    serviceAvailable.value = response.ok
  } catch (error) {
    serviceAvailable.value = false
  }
}

onMounted(() => {
  checkService()
  // 每30秒检查一次服务状态
  checkInterval = window.setInterval(checkService, 30000)
})

onUnmounted(() => {
  if (checkInterval) {
    clearInterval(checkInterval)
  }
})
</script>

<style scoped>
.service-alert {
  margin-bottom: 20px;
}

.service-message {
  line-height: 1.6;
}

.service-instructions {
  margin: 15px 0;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.service-instructions h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.service-instructions ol {
  margin: 10px 0;
  padding-left: 20px;
}

.service-instructions ul {
  margin: 5px 0;
  padding-left: 20px;
}

.service-instructions code {
  background: #e6f7ff;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  color: #1890ff;
}

.service-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.detailed-instructions {
  margin-top: 15px;
}

.detailed-instructions h4,
.detailed-instructions h5 {
  margin: 15px 0 8px 0;
  color: #303133;
}

.detailed-instructions ul {
  margin: 8px 0;
  padding-left: 20px;
}

.detailed-instructions p {
  margin: 8px 0;
}
</style>
