# 🚀 快速启动指南

## 问题解决：MobaXterm无法启动

如果您遇到"Failed to fetch"错误或MobaXterm无法启动的问题，请按照以下步骤操作：

## 解决方案

### 方案1：启动Python本地服务（推荐）

1. **打开命令行**
   - 按 `Win + R`，输入 `cmd`，按回车
   - 或者在项目文件夹中按住 `Shift` 右键，选择"在此处打开命令窗口"

2. **进入项目目录**
   ```bash
   cd bastion-terminal
   ```

3. **启动Python服务**
   ```bash
   python local-server.py
   ```

4. **确认服务启动**
   - 看到 "Python本地服务器运行在 http://localhost:8899" 表示成功
   - 保持命令行窗口打开

5. **刷新浏览器页面**
   - 回到 http://localhost:5173/
   - 现在应该可以正常连接MobaXterm了

### 方案2：使用批处理文件

1. **双击运行**
   ```
   bastion-terminal/start-python-server.bat
   ```

2. **保持窗口打开**
   - 不要关闭弹出的命令行窗口

### 方案3：Node.js服务（备选）

如果Python不可用，可以尝试：
```bash
cd bastion-terminal
node local-server.js
```

## 验证服务状态

### 检查服务是否运行
1. 打开浏览器访问：http://localhost:8899
2. 或者在主页面查看服务状态提示

### 测试API功能
1. 打开测试页面：`bastion-terminal/test-api.html`
2. 点击各个测试按钮验证功能

## 使用流程

### 1. 启动系统
```bash
# 终端1：启动本地服务
cd bastion-terminal
python local-server.py

# 终端2：启动前端应用
npm run dev
```

### 2. 访问应用
- 主应用：http://localhost:5173/
- API测试：file:///[项目路径]/test-api.html

### 3. 连接服务器
1. 在左侧选择目标服务器
2. 点击右侧的终端工具卡片
3. 在连接确认对话框中点击"立即连接"
4. MobaXterm将自动启动并连接到服务器

## 常见问题

### Q: 显示"本地服务未启动"
**A:** 按照上述方案1启动Python服务

### Q: Python命令不存在
**A:** 
1. 安装Python：https://www.python.org/downloads/
2. 或者使用Node.js：`node local-server.js`

### Q: 端口8899被占用
**A:** 
1. 检查是否已有服务在运行
2. 或者修改 `local-server.py` 中的端口号

### Q: MobaXterm路径不正确
**A:** 
1. 进入"工具管理"页面
2. 编辑MobaXterm工具
3. 修正程序路径

## 功能说明

### 当本地服务运行时
- ✅ 真正启动本地终端工具
- ✅ 检查工具程序是否存在
- ✅ 网络连接测试
- ✅ 进程管理和监控

### 当本地服务未运行时（模拟模式）
- ⚠️ 显示模拟效果，不会真正启动程序
- ⚠️ 显示桌面通知提示
- ⚠️ 所有功能都是模拟的

## 安全说明

- 本地服务仅监听 `localhost:8899`
- 不会暴露到外网
- 只能启动预配置的工具程序
- 具有完善的参数验证和错误处理

## 技术支持

如果仍有问题，请：
1. 检查控制台错误信息
2. 查看本地服务日志
3. 确认工具路径配置正确
4. 验证网络连接正常

---

**重要提示：** 为了获得完整功能，强烈建议启动本地服务。模拟模式仅用于演示和测试。
