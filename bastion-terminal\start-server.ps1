Write-Host "启动堡垒机终端管理系统本地服务..." -ForegroundColor Green
Write-Host ""
Write-Host "本服务用于处理系统调用，包括："
Write-Host "- 启动本地终端工具（XShell、MobaXterm、PuTTY等）"
Write-Host "- 检查工具是否存在"
Write-Host "- 网络连接测试"
Write-Host "- 进程管理"
Write-Host ""
Write-Host "服务地址: http://localhost:8899" -ForegroundColor Yellow
Write-Host ""

# 切换到脚本所在目录
Set-Location $PSScriptRoot

# 启动Python服务器
try {
    python local-server.py
} catch {
    Write-Host "启动失败: $_" -ForegroundColor Red
    Read-Host "按任意键退出"
}
