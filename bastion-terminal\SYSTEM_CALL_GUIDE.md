# 系统调用功能使用指南

## 概述

为了让堡垒机终端管理系统能够真正调用本地终端工具（如XShell、MobaXterm、PuTTY等），我们实现了一个本地HTTP服务来处理系统调用。

## 架构说明

```
前端应用 (Vue3) ←→ 本地HTTP服务 (Node.js) ←→ 系统命令/进程
```

- **前端应用**: 运行在浏览器中，提供用户界面
- **本地HTTP服务**: 运行在本地8899端口，处理系统调用
- **系统命令**: 实际的终端工具程序

## 启动步骤

### 1. 启动本地服务

有两种方式启动本地服务：

#### 方式一：使用批处理文件（推荐）
```bash
# 双击运行
start-local-server.bat
```

#### 方式二：手动启动
```bash
node local-server.js
```

### 2. 启动前端应用

```bash
npm run dev
```

### 3. 访问应用

打开浏览器访问：http://localhost:5173/

## 功能说明

### 支持的系统调用

1. **执行命令** (`/execute`)
   - 启动本地终端工具
   - 返回进程ID
   - 支持参数解析

2. **检查工具** (`/check-tool`)
   - 检查工具程序是否存在
   - 验证文件路径

3. **网络测试** (`/test-connection`)
   - 使用ping命令测试连接
   - 返回延迟信息

4. **进程管理** (`/get-processes`, `/kill-process`)
   - 获取运行中的进程列表
   - 终止指定进程

### 安全特性

- **CORS支持**: 允许前端应用跨域访问
- **参数验证**: 防止恶意命令注入
- **进程隔离**: 启动的进程独立运行
- **错误处理**: 完善的错误捕获和提示

## 使用示例

### 连接服务器

1. 在主面板选择目标服务器
2. 点击终端工具卡片
3. 在连接确认对话框中点击"立即连接"
4. 系统会：
   - 检查工具是否存在
   - 测试网络连接
   - 启动终端工具
   - 显示进程信息

### 管理进程

1. 在主面板的"运行中的终端"区域
2. 查看当前运行的进程
3. 点击"终止"按钮结束进程
4. 点击"刷新"更新进程列表

## 故障排除

### 常见问题

1. **本地服务未启动**
   ```
   错误: 本地服务未启动，请先运行: node local-server.js
   解决: 运行 start-local-server.bat 启动本地服务
   ```

2. **工具程序不存在**
   ```
   错误: 工具程序不存在: C:\Program Files\...
   解决: 在工具管理页面检查并修正工具路径
   ```

3. **网络连接失败**
   ```
   错误: 连接超时或主机不可达
   解决: 检查目标服务器网络连接
   ```

4. **进程启动失败**
   ```
   错误: 执行失败: spawn ENOENT
   解决: 确认工具路径正确且有执行权限
   ```

### 调试方法

1. **查看本地服务日志**
   - 本地服务会在控制台输出详细日志
   - 包括执行的命令和参数

2. **查看浏览器控制台**
   - 打开开发者工具
   - 查看Network和Console标签

3. **测试本地服务**
   ```bash
   curl -X POST http://localhost:8899/check-tool \
     -H "Content-Type: application/json" \
     -d '{"toolPath":"C:\\Windows\\System32\\notepad.exe"}'
   ```

## 扩展功能

### 添加新的终端工具

1. 在工具管理页面添加工具
2. 配置正确的程序路径
3. 设置参数模板
4. 测试连接功能

### 自定义参数模板

支持的变量：
- `{username}`: 服务器用户名
- `{host}`: 服务器主机地址
- `{port}`: 服务器端口

示例：
```
XShell: -url ssh://{username}@{host}:{port}
MobaXterm: -newtab "ssh {username}@{host} -p {port}"
PuTTY: -ssh {host} -P {port} -l {username}
```

## 安全注意事项

1. **本地服务仅监听localhost**
   - 不会暴露到外网
   - 只能本机访问

2. **参数过滤**
   - 防止命令注入
   - 验证文件路径

3. **进程管理**
   - 只能管理自己启动的进程
   - 不能访问系统关键进程

## 生产环境部署

### 方案1：Electron应用
- 将整个应用打包为桌面应用
- 内置本地服务功能
- 更好的系统集成

### 方案2：系统服务
- 将本地服务注册为Windows服务
- 开机自动启动
- 更稳定的运行环境

### 方案3：企业级集成
- 与现有堡垒机系统集成
- 统一的用户认证
- 集中的权限管理

## 技术细节

### 进程启动方式
```javascript
const child = spawn(command, argArray, {
  detached: true,    // 分离进程
  stdio: 'ignore'    // 忽略输入输出
});
child.unref();       // 不阻塞主进程退出
```

### 参数解析
- 支持引号包围的参数
- 正确处理空格和特殊字符
- 防止命令注入

### CORS配置
```javascript
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type'
};
```

## 总结

通过本地HTTP服务的方式，我们成功实现了：

1. ✅ 真正的系统调用功能
2. ✅ 安全的进程管理
3. ✅ 完善的错误处理
4. ✅ 友好的用户体验

这种架构既保证了安全性，又提供了良好的扩展性，是Web应用调用本地程序的理想解决方案。
