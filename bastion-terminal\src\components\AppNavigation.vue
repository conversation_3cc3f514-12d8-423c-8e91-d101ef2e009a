<template>
  <div class="app-navigation">
    <div class="nav-content">
      <div class="nav-brand">
        <el-icon :size="24"><Monitor /></el-icon>
        <span>堡垒机终端</span>
      </div>
      
      <div class="nav-menu">
        <router-link
          to="/"
          class="nav-item"
          :class="{ active: $route.path === '/' }"
        >
          <el-icon><Monitor /></el-icon>
          <span>主面板</span>
        </router-link>
        
        <router-link
          to="/tools"
          class="nav-item"
          :class="{ active: $route.path === '/tools' }"
        >
          <el-icon><Tools /></el-icon>
          <span>工具管理</span>
        </router-link>

        <router-link
          to="/servers"
          class="nav-item"
          :class="{ active: $route.path === '/servers' }"
        >
          <el-icon><Monitor /></el-icon>
          <span>服务器管理</span>
        </router-link>
      </div>
      
      <div class="nav-actions">
        <el-button size="small" text>
          <el-icon><Setting /></el-icon>
          设置
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 无需额外逻辑
</script>

<style scoped>
.app-navigation {
  background: #fff;
  border-bottom: 1px solid #E4E7ED;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.nav-menu {
  display: flex;
  gap: 8px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 6px;
  text-decoration: none;
  color: #606266;
  transition: all 0.3s ease;
  font-size: 14px;
}

.nav-item:hover {
  background: #F5F7FA;
  color: #409EFF;
}

.nav-item.active {
  background: #E1F3FF;
  color: #409EFF;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

@media (max-width: 768px) {
  .nav-content {
    padding: 0 16px;
  }
  
  .nav-brand span {
    display: none;
  }
  
  .nav-item span {
    display: none;
  }
}
</style>
