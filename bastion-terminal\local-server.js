import http from 'http';
import { spawn, exec } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const PORT = 8899;

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Content-Type': 'application/json'
};

// 存储运行中的进程
const runningProcesses = new Map();

const server = http.createServer((req, res) => {
  // 处理CORS预检请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200, corsHeaders);
    res.end();
    return;
  }

  // 设置CORS头
  Object.keys(corsHeaders).forEach(key => {
    res.setHeader(key, corsHeaders[key]);
  });

  const url = new URL(req.url, `http://localhost:${PORT}`);
  
  if (req.method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    
    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        
        switch (url.pathname) {
          case '/execute':
            handleExecute(data, res);
            break;
          case '/check-tool':
            handleCheckTool(data, res);
            break;
          case '/test-connection':
            handleTestConnection(data, res);
            break;
          case '/get-processes':
            handleGetProcesses(res);
            break;
          case '/kill-process':
            handleKillProcess(data, res);
            break;
          default:
            res.writeHead(404);
            res.end(JSON.stringify({ error: 'Not found' }));
        }
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({ error: 'Invalid JSON' }));
      }
    });
  } else {
    res.writeHead(405);
    res.end(JSON.stringify({ error: 'Method not allowed' }));
  }
});

// 执行命令
function handleExecute(data, res) {
  const { command, args } = data;
  
  console.log(`执行命令: ${command}`);
  console.log(`参数: ${args}`);
  
  try {
    // 检查工具是否存在
    if (!fs.existsSync(command)) {
      res.writeHead(200);
      res.end(JSON.stringify({
        success: false,
        message: `工具程序不存在: ${command}`
      }));
      return;
    }
    
    // 解析参数
    const argArray = parseArgs(args);
    
    // 启动进程
    const child = spawn(command, argArray, {
      detached: true,
      stdio: 'ignore'
    });
    
    // 分离进程，让它独立运行
    child.unref();
    
    const pid = child.pid;
    
    // 存储进程信息
    runningProcesses.set(pid, {
      pid: pid,
      name: path.basename(command),
      command: `${command} ${args}`,
      startTime: new Date()
    });
    
    console.log(`进程启动成功，PID: ${pid}`);
    
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      message: `成功启动程序，进程ID: ${pid}`,
      pid: pid
    }));
    
  } catch (error) {
    console.error('执行命令失败:', error);
    res.writeHead(200);
    res.end(JSON.stringify({
      success: false,
      message: `执行失败: ${error.message}`
    }));
  }
}

// 检查工具是否存在
function handleCheckTool(data, res) {
  const { toolPath } = data;
  const exists = fs.existsSync(toolPath);
  
  res.writeHead(200);
  res.end(JSON.stringify({ exists }));
}

// 测试网络连接
function handleTestConnection(data, res) {
  const { host, port } = data;
  
  // 使用ping命令测试连接
  exec(`ping -n 1 -w 1000 ${host}`, (error, stdout, stderr) => {
    if (error) {
      res.writeHead(200);
      res.end(JSON.stringify({
        success: false,
        error: '连接超时或主机不可达'
      }));
    } else {
      // 提取延迟信息
      const latencyMatch = stdout.match(/时间[<=](\d+)ms/);
      const latency = latencyMatch ? parseInt(latencyMatch[1]) : 0;
      
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        latency: latency
      }));
    }
  });
}

// 获取进程列表
function handleGetProcesses(res) {
  // 清理已结束的进程
  for (const [pid, process] of runningProcesses.entries()) {
    try {
      // 检查进程是否还在运行
      process.kill(0); // 发送信号0检查进程是否存在
    } catch (error) {
      // 进程不存在，从列表中移除
      runningProcesses.delete(pid);
    }
  }
  
  const processes = Array.from(runningProcesses.values());
  
  res.writeHead(200);
  res.end(JSON.stringify(processes));
}

// 终止进程
function handleKillProcess(data, res) {
  const { pid } = data;
  
  try {
    process.kill(pid, 'SIGTERM');
    runningProcesses.delete(pid);
    
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      message: `进程 ${pid} 已终止`
    }));
  } catch (error) {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: false,
      message: `终止进程失败: ${error.message}`
    }));
  }
}

// 解析参数字符串
function parseArgs(argsString) {
  const args = [];
  let current = '';
  let inQuotes = false;
  
  for (let i = 0; i < argsString.length; i++) {
    const char = argsString[i];
    
    if (char === '"') {
      inQuotes = !inQuotes;
    } else if (char === ' ' && !inQuotes) {
      if (current) {
        args.push(current);
        current = '';
      }
    } else {
      current += char;
    }
  }
  
  if (current) {
    args.push(current);
  }
  
  return args;
}

server.listen(PORT, 'localhost', () => {
  console.log(`本地服务器运行在 http://localhost:${PORT}`);
  console.log('用于处理堡垒机终端管理系统的本地调用');
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  console.log('\n正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});
