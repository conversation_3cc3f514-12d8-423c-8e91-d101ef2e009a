<template>
  <el-card 
    class="terminal-card" 
    :class="{ 'disabled': !tool.enabled }"
    shadow="hover"
    @click="handleClick"
  >
    <template #header>
      <div class="card-header">
        <el-icon :size="24" :color="tool.enabled ? '#409EFF' : '#C0C4CC'">
          <component :is="tool.icon" />
        </el-icon>
        <span class="tool-name">{{ tool.name }}</span>
        <el-switch 
          v-model="tool.enabled" 
          @change="handleToggle"
          @click.stop
        />
      </div>
    </template>
    
    <div class="card-content">
      <p class="description">{{ tool.description }}</p>
      <div class="tool-info">
        <el-tag size="small" type="info">
          <el-icon><FolderOpened /></el-icon>
          {{ getShortPath(tool.path) }}
        </el-tag>
      </div>
      
      <div class="action-buttons" v-if="showActions">
        <el-button 
          type="primary" 
          size="small" 
          :disabled="!tool.enabled || !selectedServer"
          @click.stop="handleConnect"
        >
          <el-icon><Connection /></el-icon>
          连接
        </el-button>
        <el-button 
          size="small" 
          @click.stop="handleEdit"
        >
          <el-icon><Edit /></el-icon>
          编辑
        </el-button>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { TerminalTool, ServerInfo } from '@/stores/terminal'

interface Props {
  tool: TerminalTool
  selectedServer?: ServerInfo | null
  showActions?: boolean
}

interface Emits {
  (e: 'connect', toolId: string): void
  (e: 'edit', tool: TerminalTool): void
  (e: 'toggle', toolId: string, enabled: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  showActions: true
})

const emit = defineEmits<Emits>()

const getShortPath = (path: string) => {
  const parts = path.split('\\')
  if (parts.length > 3) {
    return `...\\${parts.slice(-2).join('\\')}`
  }
  return path
}

const handleClick = () => {
  if (props.tool.enabled && props.selectedServer && props.showActions) {
    handleConnect()
  }
}

const handleConnect = () => {
  if (props.tool.enabled && props.selectedServer) {
    emit('connect', props.tool.id)
  }
}

const handleEdit = () => {
  emit('edit', props.tool)
}

const handleToggle = (enabled: boolean) => {
  emit('toggle', props.tool.id, enabled)
}
</script>

<style scoped>
.terminal-card {
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100%;
}

.terminal-card:hover {
  transform: translateY(-2px);
}

.terminal-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.terminal-card.disabled:hover {
  transform: none;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.tool-name {
  flex: 1;
  font-weight: 600;
  font-size: 16px;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.description {
  color: #606266;
  font-size: 14px;
  margin: 0;
  line-height: 1.4;
}

.tool-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tool-info .el-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.action-buttons .el-button {
  flex: 1;
}
</style>
