<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useTerminalStore } from '@/stores/terminal'
import TerminalCard from '@/components/TerminalCard.vue'
import ServerList from '@/components/ServerList.vue'
import ConnectionDialog from '@/components/ConnectionDialog.vue'
import AppNavigation from '@/components/AppNavigation.vue'
import type { ServerInfo, TerminalTool } from '@/stores/terminal'

const terminalStore = useTerminalStore()

// 响应式数据
const selectedServer = ref<ServerInfo | null>(null)
const showConnectionDialog = ref(false)
const selectedTool = ref<TerminalTool | null>(null)

// 计算属性
const enabledTools = computed(() =>
  terminalStore.tools.filter(tool => tool.enabled)
)

const recentConnections = computed(() =>
  terminalStore.connectionHistory.slice(0, 5)
)

// 事件处理
const handleSelectServer = (server: ServerInfo) => {
  selectedServer.value = server
  terminalStore.selectedServer = server
}

const handleConnectTool = (toolId: string) => {
  if (!selectedServer.value) {
    ElMessage.warning('请先选择一个服务器')
    return
  }

  const tool = terminalStore.tools.find(t => t.id === toolId)
  if (!tool) {
    ElMessage.error('工具不存在')
    return
  }

  selectedTool.value = tool
  showConnectionDialog.value = true
}

const handleConnect = async (data: { serverId: string, toolId: string, options: any }) => {
  try {
    const result = await terminalStore.connectToServer(data.serverId, data.toolId)
    ElMessage.success(`正在启动 ${result.tool.name} 连接到 ${result.server.name}`)

    // 这里可以调用系统API启动工具
    console.log('执行命令:', result.command)
  } catch (error) {
    ElMessage.error('连接失败: ' + (error as Error).message)
  }
}

const handleEditTool = (tool: TerminalTool) => {
  ElMessage.info('编辑工具功能待实现')
}

const handleToggleTool = (toolId: string, enabled: boolean) => {
  terminalStore.updateTool(toolId, { enabled })
  ElMessage.success(enabled ? '工具已启用' : '工具已禁用')
}

const formatConnectionTime = (date: Date) => {
  return date.toLocaleString()
}
</script>

<template>
  <div class="dashboard">
    <!-- 导航栏 -->
    <AppNavigation />

    <!-- 头部信息 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="title-section">
          <h1>
            <el-icon><Monitor /></el-icon>
            堡垒机终端管理
          </h1>
          <p>通过工具助手调用本地终端工具，安全便捷地管理服务器连接</p>
        </div>
        <div class="stats-section">
          <el-statistic
            title="可用工具"
            :value="enabledTools.length"
            suffix="个"
          >
            <template #prefix>
              <el-icon><Tools /></el-icon>
            </template>
          </el-statistic>
          <el-statistic
            title="服务器"
            :value="terminalStore.servers.length"
            suffix="台"
          >
            <template #prefix>
              <el-icon><Server /></el-icon>
            </template>
          </el-statistic>
          <el-statistic
            title="今日连接"
            :value="recentConnections.length"
            suffix="次"
          >
            <template #prefix>
              <el-icon><Connection /></el-icon>
            </template>
          </el-statistic>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="dashboard-content">
      <!-- 左侧：服务器列表 -->
      <div class="left-panel">
        <el-card shadow="never" class="panel-card">
          <template #header>
            <div class="panel-header">
              <h3>
                <el-icon><Server /></el-icon>
                服务器列表
              </h3>
              <el-button size="small" type="primary" text>
                <el-icon><Plus /></el-icon>
                添加服务器
              </el-button>
            </div>
          </template>
          <ServerList
            :servers="terminalStore.servers"
            :selected-server="selectedServer"
            @select="handleSelectServer"
          />
        </el-card>
      </div>

      <!-- 右侧：工具和连接信息 -->
      <div class="right-panel">
        <!-- 选中的服务器信息 -->
        <el-card v-if="selectedServer" shadow="never" class="server-info-card">
          <template #header>
            <div class="panel-header">
              <h3>
                <el-icon><InfoFilled /></el-icon>
                当前选中服务器
              </h3>
            </div>
          </template>
          <div class="selected-server-info">
            <div class="server-details">
              <h4>{{ selectedServer.name }}</h4>
              <p class="server-address">{{ selectedServer.username }}@{{ selectedServer.host }}:{{ selectedServer.port }}</p>
              <p class="server-description">{{ selectedServer.description }}</p>
              <div class="server-tags">
                <el-tag
                  v-for="tag in selectedServer.tags"
                  :key="tag"
                  size="small"
                  type="info"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 终端工具 -->
        <el-card shadow="never" class="panel-card">
          <template #header>
            <div class="panel-header">
              <h3>
                <el-icon><Tools /></el-icon>
                终端工具
              </h3>
              <el-button
                size="small"
                type="primary"
                text
                @click="$router.push('/tools')"
              >
                <el-icon><Setting /></el-icon>
                管理工具
              </el-button>
            </div>
          </template>
          <div class="tools-grid">
            <TerminalCard
              v-for="tool in terminalStore.tools"
              :key="tool.id"
              :tool="tool"
              :selected-server="selectedServer"
              @connect="handleConnectTool"
              @edit="handleEditTool"
              @toggle="handleToggleTool"
            />
          </div>
        </el-card>

        <!-- 连接历史 -->
        <el-card v-if="recentConnections.length > 0" shadow="never" class="panel-card">
          <template #header>
            <div class="panel-header">
              <h3>
                <el-icon><Clock /></el-icon>
                最近连接
              </h3>
              <el-button size="small" type="primary" text>
                查看全部
              </el-button>
            </div>
          </template>
          <div class="connection-history">
            <div
              v-for="connection in recentConnections"
              :key="connection.id"
              class="history-item"
            >
              <div class="history-info">
                <div class="history-header">
                  <span class="server-name">{{ connection.serverName }}</span>
                  <el-tag size="small" :type="connection.status === 'success' ? 'success' : 'danger'">
                    {{ connection.status === 'success' ? '成功' : '失败' }}
                  </el-tag>
                </div>
                <div class="history-details">
                  <span class="tool-name">{{ connection.toolName }}</span>
                  <span class="connection-time">{{ formatConnectionTime(connection.connectedAt) }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 连接对话框 -->
    <ConnectionDialog
      v-model="showConnectionDialog"
      :server="selectedServer"
      :tool="selectedTool"
      @connect="handleConnect"
    />
  </div>
</template>

<style scoped>
.dashboard {
  min-height: 100vh;
  background: #F5F7FA;
}

.dashboard-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px 24px;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 32px;
}

.title-section h1 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 600;
}

.title-section p {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

.stats-section {
  display: flex;
  gap: 48px;
}

.stats-section :deep(.el-statistic) {
  text-align: center;
}

.stats-section :deep(.el-statistic__head) {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.stats-section :deep(.el-statistic__content) {
  color: white;
  font-size: 24px;
  font-weight: 600;
}

.dashboard-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 24px;
  align-items: start;
}

.left-panel, .right-panel {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.panel-card {
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.server-info-card {
  background: linear-gradient(135deg, #E3F2FD 0%, #F3E5F5 100%);
}

.selected-server-info {
  padding: 8px 0;
}

.server-details h4 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #303133;
}

.server-address {
  margin: 0 0 8px 0;
  font-family: 'Courier New', monospace;
  color: #606266;
  font-size: 14px;
}

.server-description {
  margin: 0 0 12px 0;
  color: #606266;
  line-height: 1.5;
}

.server-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.connection-history {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  padding: 16px;
  background: #F8F9FA;
  border-radius: 8px;
  border-left: 4px solid #409EFF;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.server-name {
  font-weight: 600;
  color: #303133;
}

.history-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #909399;
}

.tool-name {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 1fr;
    max-width: 800px;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }

  .stats-section {
    gap: 32px;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: 24px 16px;
  }

  .dashboard-content {
    padding: 16px;
    gap: 16px;
  }

  .tools-grid {
    grid-template-columns: 1fr;
  }

  .stats-section {
    flex-direction: column;
    gap: 16px;
  }
}
</style>
