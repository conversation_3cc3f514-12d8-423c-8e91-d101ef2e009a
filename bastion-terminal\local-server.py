#!/usr/bin/env python3
import http.server
import socketserver
import json
import subprocess
import os
import sys
from urllib.parse import urlparse, parse_qs
import threading
import time

PORT = 8899

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

    def do_POST(self):
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        try:
            data = json.loads(post_data.decode('utf-8'))
            print(f"收到请求: {self.path}", data)
            
            if self.path == '/execute':
                self.handle_execute(data)
            elif self.path == '/check-tool':
                self.handle_check_tool(data)
            elif self.path == '/test-connection':
                self.handle_test_connection(data)
            elif self.path == '/get-processes':
                self.handle_get_processes()
            elif self.path == '/kill-process':
                self.handle_kill_process(data)
            else:
                self.send_error_response(404, 'Not found')
                
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            self.send_error_response(400, 'Invalid JSON')
        except Exception as e:
            print(f"处理请求错误: {e}")
            self.send_error_response(500, str(e))

    def handle_execute(self, data):
        command = data.get('command', '')
        args = data.get('args', '')
        
        print(f"执行命令: {command}")
        print(f"参数: {args}")
        
        try:
            # 检查工具是否存在
            if not os.path.exists(command):
                print(f"工具不存在: {command}")
                self.send_json_response({
                    'success': False,
                    'message': f'工具程序不存在: {command}'
                })
                return
            
            # 解析参数
            if args:
                # 简单的参数解析
                arg_list = []
                current_arg = ''
                in_quotes = False
                
                for char in args:
                    if char == '"':
                        in_quotes = not in_quotes
                    elif char == ' ' and not in_quotes:
                        if current_arg:
                            arg_list.append(current_arg)
                            current_arg = ''
                    else:
                        current_arg += char
                
                if current_arg:
                    arg_list.append(current_arg)
            else:
                arg_list = []
            
            print(f"解析后的参数: {arg_list}")
            
            # 启动进程
            if sys.platform == 'win32':
                # Windows
                process = subprocess.Popen(
                    [command] + arg_list,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL
                )
            else:
                # Unix/Linux
                process = subprocess.Popen(
                    [command] + arg_list,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL
                )
            
            pid = process.pid
            print(f"进程启动成功，PID: {pid}")
            
            self.send_json_response({
                'success': True,
                'message': f'成功启动程序，进程ID: {pid}',
                'pid': pid
            })
            
        except Exception as e:
            print(f"执行命令失败: {e}")
            self.send_json_response({
                'success': False,
                'message': f'执行失败: {str(e)}'
            })

    def handle_check_tool(self, data):
        tool_path = data.get('toolPath', '')
        print(f"检查工具: {tool_path}")
        
        exists = os.path.exists(tool_path)
        print(f"工具存在: {exists}")
        
        self.send_json_response({'exists': exists})

    def handle_test_connection(self, data):
        host = data.get('host', '')
        port = data.get('port', 22)
        print(f"测试连接: {host}:{port}")
        
        try:
            # 使用ping测试连接
            if sys.platform == 'win32':
                result = subprocess.run(['ping', '-n', '1', '-w', '1000', host], 
                                      capture_output=True, text=True, timeout=5)
            else:
                result = subprocess.run(['ping', '-c', '1', '-W', '1', host], 
                                      capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                print("连接测试成功")
                self.send_json_response({
                    'success': True,
                    'latency': 50  # 简化的延迟值
                })
            else:
                print("连接测试失败")
                self.send_json_response({
                    'success': False,
                    'error': '连接超时或主机不可达'
                })
                
        except Exception as e:
            print(f"连接测试异常: {e}")
            self.send_json_response({
                'success': False,
                'error': '网络测试失败'
            })

    def handle_get_processes(self):
        print("获取进程列表")
        # 简化实现，返回空列表
        self.send_json_response([])

    def handle_kill_process(self, data):
        pid = data.get('pid', 0)
        print(f"终止进程: {pid}")
        
        try:
            if sys.platform == 'win32':
                subprocess.run(['taskkill', '/F', '/PID', str(pid)], check=True)
            else:
                os.kill(pid, 9)
            
            self.send_json_response({
                'success': True,
                'message': f'进程 {pid} 已终止'
            })
        except Exception as e:
            print(f"终止进程失败: {e}")
            self.send_json_response({
                'success': False,
                'message': f'终止进程失败: {str(e)}'
            })

    def send_json_response(self, data):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        response = json.dumps(data, ensure_ascii=False)
        self.wfile.write(response.encode('utf-8'))

    def send_error_response(self, code, message):
        self.send_response(code)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        response = json.dumps({'error': message}, ensure_ascii=False)
        self.wfile.write(response.encode('utf-8'))

def main():
    try:
        with socketserver.TCPServer(("localhost", PORT), CORSHTTPRequestHandler) as httpd:
            print(f"Python本地服务器运行在 http://localhost:{PORT}")
            print("用于处理堡垒机终端管理系统的本地调用")
            print("按 Ctrl+C 停止服务器")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n正在关闭服务器...")
        print("服务器已关闭")
    except Exception as e:
        print(f"服务器启动失败: {e}")

if __name__ == "__main__":
    main()
