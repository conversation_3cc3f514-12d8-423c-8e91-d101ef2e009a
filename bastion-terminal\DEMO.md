# 堡垒机终端管理系统 - 演示说明

## 系统概览

本系统是一个现代化的堡垒机终端管理前端应用，旨在为运维人员提供便捷的服务器连接管理和本地终端工具调用功能。

## 主要功能演示

### 1. 主面板 (/)

主面板是系统的核心界面，包含以下主要区域：

#### 头部统计区域
- **可用工具数量**: 显示当前启用的终端工具数量
- **服务器数量**: 显示系统中配置的服务器总数
- **今日连接次数**: 显示当天的连接统计

#### 左侧服务器列表
- **分组显示**: 服务器按照环境分组（生产环境、测试环境等）
- **搜索功能**: 支持按服务器名称、IP地址、标签搜索
- **服务器信息**: 显示服务器名称、连接地址、描述和标签
- **连接状态**: 显示最后连接时间和连接状态

#### 右侧工具区域
- **选中服务器信息**: 显示当前选中服务器的详细信息
- **终端工具卡片**: 以卡片形式展示可用的终端工具
- **连接历史**: 显示最近的连接记录

### 2. 工具管理页面 (/tools)

工具管理页面提供完整的终端工具配置功能：

#### 工具列表表格
- **工具信息**: 显示工具名称、描述、程序路径、参数模板
- **状态管理**: 可以启用/禁用工具
- **操作按钮**: 编辑和删除工具

#### 添加/编辑工具对话框
- **基本信息**: 工具名称、图标选择、描述
- **程序配置**: 程序路径和参数模板
- **参数变量**: 支持 {username}、{host}、{port} 变量

### 3. 连接流程演示

#### 步骤1: 选择服务器
1. 在主面板左侧服务器列表中选择目标服务器
2. 服务器信息会在右侧显示
3. 终端工具卡片会显示"连接"按钮

#### 步骤2: 选择工具
1. 点击任意启用的终端工具卡片
2. 系统会弹出连接确认对话框

#### 步骤3: 确认连接
1. 连接对话框显示：
   - 选中的终端工具信息
   - 目标服务器详情
   - 生成的连接命令预览
2. 可以复制命令到剪贴板
3. 点击"立即连接"执行连接

## 预配置的示例数据

### 终端工具
1. **XShell** - 专业的SSH客户端工具
2. **MobaXterm** - 增强型终端工具
3. **PuTTY** - 轻量级SSH客户端
4. **WinSCP** - SFTP文件传输工具

### 示例服务器
1. **生产服务器-Web01**
   - 地址: root@*************:22
   - 标签: web, nginx, production

2. **测试服务器-DB01**
   - 地址: admin@*************:22
   - 标签: database, mysql, test

## 界面特性

### 响应式设计
- 支持桌面端和移动端访问
- 自适应布局，在不同屏幕尺寸下都有良好体验

### 现代化UI
- 使用 Element Plus 组件库
- 渐变色背景和卡片式设计
- 图标和动画效果增强用户体验

### 交互反馈
- 操作成功/失败的消息提示
- 加载状态和确认对话框
- 悬停效果和状态变化

## 技术实现亮点

### 状态管理
- 使用 Pinia 进行全局状态管理
- 响应式数据更新
- 本地数据持久化（可扩展）

### 组件化设计
- 高度模块化的组件结构
- 可复用的UI组件
- 清晰的数据流和事件传递

### TypeScript支持
- 完整的类型定义
- 编译时类型检查
- 更好的开发体验

## 扩展可能性

### 后端集成
- 可以轻松集成REST API
- 支持实时数据更新
- 用户认证和权限管理

### 功能增强
- 批量操作支持
- 连接会话管理
- 审计日志记录
- 文件传输功能

### 系统集成
- 与现有堡垒机系统集成
- 单点登录(SSO)支持
- 企业级安全策略

## 使用建议

1. **首次使用**: 建议先在工具管理页面检查和配置工具路径
2. **服务器管理**: 根据实际环境配置服务器信息和分组
3. **权限控制**: 在生产环境中应该集成适当的权限管理
4. **安全考虑**: 敏感信息应该加密存储，避免明文保存密码

## 浏览器访问

开发模式下访问地址：http://localhost:5173/

主要页面：
- 主面板: http://localhost:5173/
- 工具管理: http://localhost:5173/tools
