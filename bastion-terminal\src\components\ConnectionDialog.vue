<template>
  <el-dialog
    v-model="visible"
    title="连接确认"
    width="500px"
    :before-close="handleClose"
  >
    <div class="connection-info" v-if="server && tool">
      <div class="info-section">
        <h4>
          <el-icon><Monitor /></el-icon>
          终端工具
        </h4>
        <div class="tool-info">
          <div class="tool-header">
            <el-icon :size="20">
              <component :is="tool.icon" />
            </el-icon>
            <span class="tool-name">{{ tool.name }}</span>
          </div>
          <p class="tool-description">{{ tool.description }}</p>
          <el-tag size="small" type="info">
            {{ tool.path }}
          </el-tag>
        </div>
      </div>

      <el-divider />

      <div class="info-section">
        <h4>
          <el-icon><Server /></el-icon>
          目标服务器
        </h4>
        <div class="server-info">
          <div class="server-header">
            <span class="server-name">{{ server.name }}</span>
            <el-tag size="small">{{ server.group }}</el-tag>
          </div>
          <p class="server-address">{{ server.username }}@{{ server.host }}:{{ server.port }}</p>
          <p class="server-description">{{ server.description }}</p>
          <div class="server-tags">
            <el-tag 
              v-for="tag in server.tags" 
              :key="tag"
              size="small"
              type="info"
            >
              {{ tag }}
            </el-tag>
          </div>
        </div>
      </div>

      <el-divider />

      <div class="info-section">
        <h4>
          <el-icon><Terminal /></el-icon>
          执行命令
        </h4>
        <div class="command-preview">
          <el-input
            :value="commandPreview"
            readonly
            type="textarea"
            :rows="3"
            class="command-input"
          />
          <el-button 
            size="small" 
            type="primary" 
            text
            @click="copyCommand"
          >
            <el-icon><CopyDocument /></el-icon>
            复制命令
          </el-button>
        </div>
      </div>

      <!-- 连接选项 -->
      <div class="connection-options">
        <el-checkbox v-model="savePassword">记住密码</el-checkbox>
        <el-checkbox v-model="autoConnect">自动连接</el-checkbox>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleConnect"
          :loading="connecting"
        >
          <el-icon><Connection /></el-icon>
          {{ connecting ? '连接中...' : '立即连接' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { ServerInfo, TerminalTool } from '@/stores/terminal'

interface Props {
  modelValue: boolean
  server?: ServerInfo | null
  tool?: TerminalTool | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'connect', data: { serverId: string, toolId: string, options: any }): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const connecting = ref(false)
const savePassword = ref(false)
const autoConnect = ref(false)

// 生成命令预览
const commandPreview = computed(() => {
  if (!props.server || !props.tool) return ''
  
  let args = props.tool.args
    .replace('{username}', props.server.username)
    .replace('{host}', props.server.host)
    .replace('{port}', props.server.port.toString())

  return `${props.tool.path} ${args}`
})

const handleClose = () => {
  if (!connecting.value) {
    visible.value = false
  }
}

const handleConnect = async () => {
  if (!props.server || !props.tool) return
  
  connecting.value = true
  
  try {
    // 模拟连接延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('connect', {
      serverId: props.server.id,
      toolId: props.tool.id,
      options: {
        savePassword: savePassword.value,
        autoConnect: autoConnect.value
      }
    })
    
    ElMessage.success('连接命令已执行')
    visible.value = false
  } catch (error) {
    ElMessage.error('连接失败: ' + (error as Error).message)
  } finally {
    connecting.value = false
  }
}

const copyCommand = async () => {
  try {
    await navigator.clipboard.writeText(commandPreview.value)
    ElMessage.success('命令已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 重置状态
watch(visible, (newVisible) => {
  if (!newVisible) {
    connecting.value = false
    savePassword.value = false
    autoConnect.value = false
  }
})
</script>

<style scoped>
.connection-info {
  padding: 8px 0;
}

.info-section {
  margin-bottom: 16px;
}

.info-section h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.tool-info, .server-info {
  padding: 16px;
  background: #F8F9FA;
  border-radius: 8px;
}

.tool-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.tool-name {
  font-weight: 600;
  font-size: 16px;
}

.tool-description {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 14px;
}

.server-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.server-name {
  font-weight: 600;
  font-size: 16px;
}

.server-address {
  margin: 0 0 8px 0;
  font-family: 'Courier New', monospace;
  color: #909399;
  font-size: 14px;
}

.server-description {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 14px;
}

.server-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.command-preview {
  position: relative;
}

.command-input {
  font-family: 'Courier New', monospace;
}

.command-preview .el-button {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 1;
}

.connection-options {
  display: flex;
  gap: 16px;
  margin-top: 16px;
  padding: 16px;
  background: #F0F9FF;
  border-radius: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
}

:deep(.el-divider) {
  margin: 16px 0;
}
</style>
