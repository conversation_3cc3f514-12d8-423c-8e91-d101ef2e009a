# 堡垒机终端管理系统

一个基于 Vue 3 的堡垒机终端管理前端应用，通过工具助手调用本地终端工具（XShell、MobaXtreme、Putty等），让运维人员能够通过堡垒机的终端界面直接调用本地工具。

## 功能特性

### 🖥️ 终端工具管理
- 支持多种主流终端工具（XShell、MobaXterm、PuTTY、WinSCP）
- 可视化工具配置和管理
- 自定义工具路径和参数模板
- 工具启用/禁用状态管理

### 🖧 服务器连接管理
- 服务器信息管理（主机、端口、用户名等）
- 服务器分组和标签管理
- 连接历史记录
- 快速搜索和筛选

### 🎨 用户界面
- 现代化的响应式设计
- 直观的卡片式布局
- 实时状态显示
- 移动端适配

### 🔧 连接功能
- 一键连接服务器
- 连接确认对话框
- 命令预览和复制
- 连接状态跟踪

## 技术栈

- **前端框架**: Vue 3 + Composition API
- **构建工具**: Vite
- **UI组件库**: Element Plus
- **路由管理**: Vue Router
- **状态管理**: Pinia
- **开发语言**: TypeScript
- **样式**: CSS3 + Flexbox/Grid

## 快速开始

### 环境要求
- Node.js 16+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 代码检查
```bash
npm run lint
```

## 使用说明

### 1. 主面板
- 查看服务器列表和终端工具
- 选择服务器后可直接点击工具连接
- 查看连接历史和统计信息

### 2. 工具管理
- 添加、编辑、删除终端工具
- 配置工具路径和参数模板
- 启用/禁用工具

### 3. 连接流程
1. 在主面板选择目标服务器
2. 点击相应的终端工具
3. 在连接确认对话框中查看连接信息
4. 点击"立即连接"执行连接命令

## 参数模板

工具参数支持以下变量：
- `{username}` - 服务器用户名
- `{host}` - 服务器主机地址
- `{port}` - 服务器端口

示例：
```
XShell: -url ssh://{username}@{host}:{port}
PuTTY: -ssh {host} -P {port} -l {username}
```

## 功能完善情况

### ✅ 已实现功能

#### 核心功能
- **终端工具管理**: 完整的CRUD操作，支持工具路径选择
- **服务器管理**: 完整的服务器信息管理，支持分组和标签
- **连接功能**: 一键连接，带连接确认和状态检查
- **进程管理**: 显示运行中的终端进程，支持终止操作

#### 用户界面
- **响应式设计**: 支持桌面端和移动端
- **现代化UI**: Element Plus组件库，美观易用
- **导航系统**: 顶部导航栏，页面间无缝切换
- **实时反馈**: 操作提示、加载状态、错误处理

#### 数据管理
- **状态管理**: Pinia全局状态管理
- **数据验证**: 表单验证和数据类型检查
- **本地存储**: 支持数据持久化（可扩展）

### 🔧 技术特性

#### 系统集成模拟
- **工具检查**: 检查本地工具是否存在
- **网络测试**: 连接前的网络可达性测试
- **进程管理**: 模拟进程启动、监控和终止
- **文件选择**: 工具路径选择对话框

#### 安全特性
- **参数验证**: 防止恶意输入
- **连接确认**: 重要操作需要用户确认
- **错误处理**: 完善的错误捕获和提示

### 📱 页面功能

#### 主面板 (/)
- 服务器列表展示（分组、搜索、筛选）
- 终端工具卡片（状态管理、一键连接）
- 运行进程监控（实时刷新、进程管理）
- 连接历史记录
- 快速添加服务器
- 统计信息展示

#### 工具管理 (/tools)
- 工具列表表格展示
- 添加/编辑工具（支持文件选择）
- 工具启用/禁用
- 参数模板配置
- 图标选择

#### 服务器管理 (/servers)
- 服务器列表表格展示
- 添加/编辑服务器（完整表单）
- 服务器分组管理
- 标签系统
- 搜索和筛选
- 批量操作（导入/导出）

### 🚀 部署和运行

#### 开发环境
```bash
cd bastion-terminal
npm install
npm run dev
```
访问: http://localhost:5173/

#### 生产构建
```bash
npm run build
npm run preview
```

### 🔮 扩展建议

#### 后端集成
- REST API接口对接
- WebSocket实时通信
- 用户认证和权限管理
- 数据库持久化

#### 功能增强
- 批量连接操作
- 连接会话管理
- 文件传输功能
- 脚本执行功能
- 连接录制和回放

#### 安全增强
- 密码加密存储
- 双因子认证
- 操作审计日志
- IP白名单控制

#### 系统集成
- 与现有堡垒机系统集成
- 单点登录(SSO)
- LDAP/AD集成
- 企业级监控和告警

### 📊 性能优化

- 组件懒加载
- 虚拟滚动（大量数据）
- 缓存策略
- 代码分割

### 🛡️ 安全考虑

- XSS防护
- CSRF防护
- 输入验证
- 敏感信息保护

## 总结

本项目实现了一个功能完整、界面美观的堡垒机终端管理系统前端。所有核心功能都已实现，包括工具管理、服务器管理、连接功能和进程监控。系统采用现代化的技术栈，具有良好的可扩展性和维护性，可以作为实际项目的基础进行进一步开发。
