# 堡垒机终端管理系统

一个基于 Vue 3 的堡垒机终端管理前端应用，通过工具助手调用本地终端工具（XShell、MobaXtreme、Putty等），让运维人员能够通过堡垒机的终端界面直接调用本地工具。

## 功能特性

### 🖥️ 终端工具管理
- 支持多种主流终端工具（XShell、MobaXterm、PuTTY、WinSCP）
- 可视化工具配置和管理
- 自定义工具路径和参数模板
- 工具启用/禁用状态管理

### 🖧 服务器连接管理
- 服务器信息管理（主机、端口、用户名等）
- 服务器分组和标签管理
- 连接历史记录
- 快速搜索和筛选

### 🎨 用户界面
- 现代化的响应式设计
- 直观的卡片式布局
- 实时状态显示
- 移动端适配

### 🔧 连接功能
- 一键连接服务器
- 连接确认对话框
- 命令预览和复制
- 连接状态跟踪

## 技术栈

- **前端框架**: Vue 3 + Composition API
- **构建工具**: Vite
- **UI组件库**: Element Plus
- **路由管理**: Vue Router
- **状态管理**: Pinia
- **开发语言**: TypeScript
- **样式**: CSS3 + Flexbox/Grid

## 快速开始

### 环境要求
- Node.js 16+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 代码检查
```bash
npm run lint
```

## 使用说明

### 1. 主面板
- 查看服务器列表和终端工具
- 选择服务器后可直接点击工具连接
- 查看连接历史和统计信息

### 2. 工具管理
- 添加、编辑、删除终端工具
- 配置工具路径和参数模板
- 启用/禁用工具

### 3. 连接流程
1. 在主面板选择目标服务器
2. 点击相应的终端工具
3. 在连接确认对话框中查看连接信息
4. 点击"立即连接"执行连接命令

## 参数模板

工具参数支持以下变量：
- `{username}` - 服务器用户名
- `{host}` - 服务器主机地址
- `{port}` - 服务器端口

示例：
```
XShell: -url ssh://{username}@{host}:{port}
PuTTY: -ssh {host} -P {port} -l {username}
```
