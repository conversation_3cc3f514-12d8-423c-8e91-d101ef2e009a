<template>
  <div class="tools-manage">
    <!-- 导航栏 -->
    <AppNavigation />

    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1>
            <el-icon><Tools /></el-icon>
            工具管理
          </h1>
          <p>配置和管理本地终端工具</p>
        </div>
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          添加工具
        </el-button>
      </div>
    </div>

    <div class="page-content">
      <el-card shadow="never">
        <el-table :data="terminalStore.tools" style="width: 100%">
          <el-table-column prop="name" label="工具名称" width="150">
            <template #default="{ row }">
              <div class="tool-name-cell">
                <el-icon :size="20">
                  <component :is="row.icon" />
                </el-icon>
                <span>{{ row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" />
          <el-table-column prop="path" label="程序路径" width="300">
            <template #default="{ row }">
              <el-text class="path-text">{{ row.path }}</el-text>
            </template>
          </el-table-column>
          <el-table-column prop="args" label="参数模板" width="200">
            <template #default="{ row }">
              <el-text class="args-text">{{ row.args }}</el-text>
            </template>
          </el-table-column>
          <el-table-column prop="enabled" label="状态" width="100">
            <template #default="{ row }">
              <el-switch 
                v-model="row.enabled" 
                @change="handleToggle(row.id, row.enabled)"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="{ row }">
              <el-button size="small" @click="handleEdit(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button 
                size="small" 
                type="danger" 
                @click="handleDelete(row.id)"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 添加/编辑工具对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingTool ? '编辑工具' : '添加工具'"
      width="600px"
    >
      <el-form 
        ref="formRef"
        :model="toolForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="工具名称" prop="name">
          <el-input v-model="toolForm.name" placeholder="请输入工具名称" />
        </el-form-item>
        <el-form-item label="图标" prop="icon">
          <el-select v-model="toolForm.icon" placeholder="选择图标">
            <el-option 
              v-for="icon in iconOptions" 
              :key="icon.value"
              :label="icon.label" 
              :value="icon.value"
            >
              <div class="icon-option">
                <el-icon><component :is="icon.value" /></el-icon>
                <span>{{ icon.label }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="程序路径" prop="path">
          <div class="path-input-group">
            <el-input
              v-model="toolForm.path"
              placeholder="请输入程序完整路径"
            />
            <el-button @click="selectToolPath">
              <el-icon><FolderOpened /></el-icon>
              浏览
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="参数模板" prop="args">
          <el-input 
            v-model="toolForm.args" 
            type="textarea"
            :rows="3"
            placeholder="请输入参数模板，支持变量: {username}, {host}, {port}"
          />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="toolForm.description" 
            placeholder="请输入工具描述"
          />
        </el-form-item>
        <el-form-item label="启用状态">
          <el-switch v-model="toolForm.enabled" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave">
          {{ editingTool ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useTerminalStore } from '@/stores/terminal'
import { SystemCallUtil } from '@/utils/systemCall'
import AppNavigation from '@/components/AppNavigation.vue'
import type { TerminalTool } from '@/stores/terminal'

const terminalStore = useTerminalStore()

// 响应式数据
const showAddDialog = ref(false)
const editingTool = ref<TerminalTool | null>(null)
const formRef = ref()

// 表单数据
const toolForm = reactive({
  name: '',
  icon: 'Monitor',
  path: '',
  args: '',
  description: '',
  enabled: true
})

// 图标选项
const iconOptions = [
  { label: '监视器', value: 'Monitor' },
  { label: '终端', value: 'Terminal' },
  { label: '连接', value: 'Connection' },
  { label: '文件夹', value: 'FolderOpened' },
  { label: '服务器', value: 'Server' },
  { label: '工具', value: 'Tools' }
]

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入工具名称', trigger: 'blur' }
  ],
  path: [
    { required: true, message: '请输入程序路径', trigger: 'blur' }
  ],
  args: [
    { required: true, message: '请输入参数模板', trigger: 'blur' }
  ]
}

// 事件处理
const handleToggle = (toolId: string, enabled: boolean) => {
  terminalStore.updateTool(toolId, { enabled })
  ElMessage.success(enabled ? '工具已启用' : '工具已禁用')
}

const handleEdit = (tool: TerminalTool) => {
  editingTool.value = tool
  Object.assign(toolForm, tool)
  showAddDialog.value = true
}

const handleDelete = async (toolId: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这个工具吗？', '确认删除', {
      type: 'warning'
    })
    
    terminalStore.deleteTool(toolId)
    ElMessage.success('工具已删除')
  } catch {
    // 用户取消删除
  }
}

const handleCancel = () => {
  showAddDialog.value = false
  editingTool.value = null
  resetForm()
}

const handleSave = async () => {
  try {
    await formRef.value.validate()
    
    if (editingTool.value) {
      // 更新工具
      terminalStore.updateTool(editingTool.value.id, toolForm)
      ElMessage.success('工具已更新')
    } else {
      // 添加工具
      terminalStore.addTool(toolForm)
      ElMessage.success('工具已添加')
    }
    
    handleCancel()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const resetForm = () => {
  Object.assign(toolForm, {
    name: '',
    icon: 'Monitor',
    path: '',
    args: '',
    description: '',
    enabled: true
  })
  formRef.value?.clearValidate()
}

const selectToolPath = async () => {
  try {
    const selectedPath = await SystemCallUtil.selectFile([
      { name: '可执行文件', extensions: ['exe'] },
      { name: '所有文件', extensions: ['*'] }
    ])

    if (selectedPath) {
      toolForm.path = selectedPath
    }
  } catch (error) {
    ElMessage.error('选择文件失败')
  }
}
</script>

<style scoped>
.tools-manage {
  min-height: 100vh;
  background: #F5F7FA;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px 24px;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section h1 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 600;
}

.title-section p {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

.page-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
}

.tool-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.path-text, .args-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.icon-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.path-input-group {
  display: flex;
  gap: 8px;
}

.path-input-group .el-input {
  flex: 1;
}

:deep(.el-card) {
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table th) {
  background: #F8F9FA;
}
</style>
