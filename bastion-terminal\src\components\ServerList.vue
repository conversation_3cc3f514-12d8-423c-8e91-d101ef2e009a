<template>
  <div class="server-list">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索服务器..."
        clearable
        @input="handleSearch"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <!-- 分组显示 -->
    <div class="server-groups">
      <el-collapse v-model="activeGroups" accordion>
        <el-collapse-item 
          v-for="group in filteredGroups" 
          :key="group.name"
          :title="group.name" 
          :name="group.name"
        >
          <template #title>
            <div class="group-title">
              <el-icon><Folder /></el-icon>
              <span>{{ group.name }}</span>
              <el-badge :value="group.servers.length" class="group-badge" />
            </div>
          </template>
          
          <div class="servers-grid">
            <div 
              v-for="server in group.servers" 
              :key="server.id"
              class="server-item"
              :class="{ 'selected': selectedServer?.id === server.id }"
              @click="handleSelectServer(server)"
            >
              <div class="server-header">
                <div class="server-info">
                  <h4 class="server-name">{{ server.name }}</h4>
                  <p class="server-address">{{ server.username }}@{{ server.host }}:{{ server.port }}</p>
                </div>
                <div class="server-status">
                  <el-icon
                    :color="getStatusColor(server)"
                    :size="16"
                  >
                    <SuccessFilled v-if="server.lastConnected" />
                    <Warning v-else />
                  </el-icon>
                </div>
              </div>
              
              <p class="server-description">{{ server.description }}</p>
              
              <div class="server-tags">
                <el-tag 
                  v-for="tag in server.tags" 
                  :key="tag"
                  size="small"
                  type="info"
                >
                  {{ tag }}
                </el-tag>
              </div>
              
              <div class="server-meta" v-if="server.lastConnected">
                <el-text size="small" type="info">
                  <el-icon><Clock /></el-icon>
                  最后连接: {{ formatDate(server.lastConnected) }}
                </el-text>
              </div>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>

    <!-- 空状态 -->
    <el-empty 
      v-if="filteredGroups.length === 0"
      description="没有找到匹配的服务器"
      :image-size="100"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { ServerInfo } from '@/stores/terminal'

interface Props {
  servers: ServerInfo[]
  selectedServer?: ServerInfo | null
}

interface Emits {
  (e: 'select', server: ServerInfo): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const searchKeyword = ref('')
const activeGroups = ref<string[]>([])

// 搜索过滤后的服务器
const filteredServers = computed(() => {
  if (!searchKeyword.value) {
    return props.servers
  }
  
  return props.servers.filter(server => 
    server.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    server.host.includes(searchKeyword.value) ||
    server.description.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    server.tags.some(tag => tag.toLowerCase().includes(searchKeyword.value.toLowerCase()))
  )
})

// 按分组整理服务器
const filteredGroups = computed(() => {
  const groups = new Map<string, ServerInfo[]>()
  
  filteredServers.value.forEach(server => {
    const groupName = server.group || '未分组'
    if (!groups.has(groupName)) {
      groups.set(groupName, [])
    }
    groups.get(groupName)!.push(server)
  })
  
  return Array.from(groups.entries()).map(([name, servers]) => ({
    name,
    servers: servers.sort((a, b) => a.name.localeCompare(b.name))
  })).sort((a, b) => a.name.localeCompare(b.name))
})

// 初始化展开第一个分组
watch(filteredGroups, (newGroups) => {
  if (newGroups.length > 0 && activeGroups.value.length === 0) {
    activeGroups.value = [newGroups[0].name]
  }
}, { immediate: true })

const handleSearch = () => {
  // 搜索时展开所有分组
  if (searchKeyword.value) {
    activeGroups.value = filteredGroups.value.map(group => group.name)
  }
}

const handleSelectServer = (server: ServerInfo) => {
  emit('select', server)
}

const getStatusColor = (server: ServerInfo) => {
  return server.lastConnected ? '#67C23A' : '#E6A23C'
}

const formatDate = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  
  return date.toLocaleDateString()
}
</script>

<style scoped>
.server-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-bar {
  margin-bottom: 16px;
}

.server-groups {
  flex: 1;
  overflow-y: auto;
}

.group-title {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.group-badge {
  margin-left: auto;
}

.servers-grid {
  display: grid;
  gap: 12px;
  padding: 8px 0;
}

.server-item {
  padding: 16px;
  border: 1px solid #EBEEF5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;
}

.server-item:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.server-item.selected {
  border-color: #409EFF;
  background: #F0F9FF;
}

.server-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.server-info {
  flex: 1;
}

.server-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.server-address {
  margin: 0;
  font-size: 14px;
  color: #909399;
  font-family: 'Courier New', monospace;
}

.server-description {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.4;
}

.server-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 8px;
}

.server-meta {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

:deep(.el-collapse-item__header) {
  padding-left: 0;
  padding-right: 0;
}

:deep(.el-collapse-item__content) {
  padding-bottom: 0;
}
</style>
